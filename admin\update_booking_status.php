<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

if (isset($_GET['id']) && isset($_GET['status'])) {
    $id = sanitize_input($_GET['id']);
    $status = sanitize_input($_GET['status']);

    if (in_array($status, ['picked', 'expired'])) {
        try {
            $pdo->beginTransaction();

            // Update booking status
            $stmt = $pdo->prepare("UPDATE bookings SET status = ? WHERE id = ?");
            $stmt->execute([$status, $id]);

            // If picked, add to sales
            if ($status === 'picked') {
                $stmt = $pdo->prepare("SELECT * FROM bookings WHERE id = ?");
                $stmt->execute([$id]);
                $booking = $stmt->fetch();

                $stmt = $pdo->prepare("SELECT unit_price FROM products WHERE id = ?");
                $stmt->execute([$booking['product_id']]);
                $product = $stmt->fetch();

                $total_amount = $booking['quantity'] * $product['unit_price'];

                $stmt = $pdo->prepare("INSERT INTO sales (booking_id, total_amount) VALUES (?, ?)");
                $stmt->execute([$id, $total_amount]);
            }

            $pdo->commit();
        } catch (PDOException $e) {
            $pdo->rollBack();
            die("Error: " . $e->getMessage());
        }
    }
}

header("Location: bookings.php");
exit();
?>
