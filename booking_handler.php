<?php
require_once 'config.php';
require_once 'includes/functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $customer_name = sanitize_input($_POST['customer_name']);
    $phone = sanitize_input($_POST['phone']);
    $product_id = sanitize_input($_POST['product_id']);
    $quantity = sanitize_input($_POST['quantity']);
    $booking_date = sanitize_input($_POST['booking_date']);

    try {
        $pdo->beginTransaction();

        // Check stock
        $stmt = $pdo->prepare("SELECT stock_quantity FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();

        if ($product && $product['stock_quantity'] >= $quantity) {
            // Insert booking
            $stmt = $pdo->prepare("INSERT INTO bookings (customer_name, phone, product_id, quantity, booking_date) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$customer_name, $phone, $product_id, $quantity, $booking_date]);

            // Update stock
            $new_stock = $product['stock_quantity'] - $quantity;
            $stmt = $pdo->prepare("UPDATE products SET stock_quantity = ? WHERE id = ?");
            $stmt->execute([$new_stock, $product_id]);

            $pdo->commit();

            echo "<h1>Booking Successful!</h1>";
            echo "<p>Your booking has been placed. Thank you!</p>";
            echo '<p><a href="booking.php">Place another order</a></p>';
        } else {
            $pdo->rollBack();
            echo "<h1>Booking Failed!</h1>";
            echo "<p>Sorry, there is not enough stock for your order.</p>";
            echo '<p><a href="booking.php">Go back to the booking page</a></p>';
        }
    } catch (PDOException $e) {
        $pdo->rollBack();
        die("Error: " . $e->getMessage());
    }
} else {
    header("Location: booking.php");
    exit();
}
?>
