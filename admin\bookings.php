<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Bookings</title>
    <link href="../src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

$stmt = $pdo->query("SELECT b.*, p.name as product_name FROM bookings b JOIN products p ON b.product_id = p.id ORDER BY b.booking_date DESC");
$bookings = $stmt->fetchAll();
?>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-200 font-sans">
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg hidden md:block">
            <div class="p-6 flex items-center justify-center">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">Bakery</h1>
            </div>
            <nav class="mt-6">
                <a href="dashboard.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-tachometer-alt mr-4"></i> Dashboard
                </a>
                <a href="bookings.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-gradient-to-r from-blue-500 to-violet-500 text-white shadow-md">
                    <i class="fas fa-book-open mr-4"></i> Bookings
                </a>
                <a href="inventory.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-boxes mr-4"></i> Inventory
                </a>
                <a href="expenses.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-dollar-sign mr-4"></i> Expenses
                </a>
                <a href="reports.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-chart-line mr-4"></i> Reports
                </a>
                <a href="products.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-cookie-bite mr-4"></i> Products
                </a>
            </nav>
            <div class="p-6 mt-auto">
                <a href="../auth/logout.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-red-500 text-white shadow-md hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-4"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Topbar -->
            <header class="flex justify-between items-center p-4 bg-white dark:bg-gray-800 shadow-md">
                <div class="flex items-center">
                    <button id="menu-btn" class="text-gray-600 dark:text-gray-300 focus:outline-none md:hidden">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                        <i class="fas fa-sun"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                            <i class="fas fa-bell"></i>
                        </button>
                        <span class="absolute right-0 top-0 h-2.5 w-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></span>
                    </div>
                    <div class="relative">
                        <button class="flex items-center focus:outline-none">
                            <img src="https://via.placeholder.com/40" alt="avatar" class="h-10 w-10 rounded-full shadow-md">
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-8">
                <h2 class="text-4xl font-bold text-gray-800 dark:text-white mb-6">Manage Bookings</h2>
                
                <!-- Search and Filter -->
                <div class="mb-6 flex items-center justify-between">
                    <div class="relative w-full max-w-xs">
                        <i class="fas fa-search absolute text-gray-400 top-3 left-4"></i>
                        <input type="text" placeholder="Search by customer or product..." class="w-full pl-12 pr-4 py-2 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                    </div>
                    <div class="flex items-center space-x-4">
                        <select class="px-4 py-2 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                            <option>All Statuses</option>
                            <option>Pending</option>
                            <option>Picked</option>
                            <option>Expired</option>
                        </select>
                        <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-violet-500 text-white rounded-lg shadow-md hover:from-blue-600 hover:to-violet-600 transition-all duration-300">Filter</button>
                    </div>
                </div>

                <!-- Bookings Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($bookings as $booking): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800 dark:text-white"><?php echo htmlspecialchars($booking['customer_name']); ?></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Product: <?php echo htmlspecialchars($booking['product_name']); ?></p>
                            </div>
                            <?php
                                $status_color = 'bg-yellow-400';
                                $status_text = 'Pending';
                                if ($booking['status'] === 'picked') {
                                    $status_color = 'bg-green-400';
                                    $status_text = 'Picked';
                                } elseif ($booking['status'] === 'expired') {
                                    $status_color = 'bg-red-400';
                                    $status_text = 'Expired';
                                }
                            ?>
                            <span class="px-3 py-1 text-sm font-semibold text-white <?php echo $status_color; ?> rounded-full shadow-md"><?php echo $status_text; ?></span>
                        </div>
                        <div class="space-y-2 text-gray-600 dark:text-gray-300">
                            <p><i class="fas fa-box mr-2 text-blue-500"></i> Quantity: <?php echo $booking['quantity']; ?></p>
                            <p><i class="fas fa-calendar-alt mr-2 text-violet-500"></i> Date: <?php echo date('M d, Y H:i', strtotime($booking['booking_date'])); ?></p>
                        </div>
                        <?php if ($booking['status'] === 'pending'): ?>
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="update_booking_status.php?id=<?php echo $booking['id']; ?>&status=picked" class="px-4 py-2 bg-green-500 text-white rounded-lg shadow-md hover:bg-green-600 transition-colors duration-300"><i class="fas fa-check mr-2"></i>Mark as Picked</a>
                            <a href="update_booking_status.php?id=<?php echo $booking['id']; ?>&status=expired" class="px-4 py-2 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition-colors duration-300"><i class="fas fa-times mr-2"></i>Mark as Expired</a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </main>
        </div>
    </div>
    <script>
        // Dark mode toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark');
            const isDarkMode = document.body.classList.contains('dark');
            themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>';
            localStorage.setItem('darkMode', isDarkMode);
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }

        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.querySelector('aside');
        menuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('hidden');
        });
    </script>
</body>
</html>
