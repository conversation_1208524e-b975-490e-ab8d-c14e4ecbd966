<?php
// includes/functions.php

// Function to check if a user is logged in and has a specific role
function check_auth($role = 'admin') {
    session_start();
    if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== $role) {
        header("Location: ../auth/login.php");
        exit();
    }
}

// Function to sanitize input data
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}
?>
