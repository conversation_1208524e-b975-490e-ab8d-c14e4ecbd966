<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Expenses</title>
    <link href="../src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $expense_type = sanitize_input($_POST['expense_type']);
    $amount = sanitize_input($_POST['amount']);
    $date = sanitize_input($_POST['date']);
    $description = sanitize_input($_POST['description']);

    $stmt = $pdo->prepare("INSERT INTO expenses (expense_type, amount, date, description) VALUES (?, ?, ?, ?)");
    $stmt->execute([$expense_type, $amount, $date, $description]);

    header("Location: expenses.php");
    exit();
}

$stmt = $pdo->query("SELECT * FROM expenses ORDER BY date DESC");
$expenses = $stmt->fetchAll();
?>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-200 font-sans">
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg hidden md:block">
            <div class="p-6 flex items-center justify-center">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">Bakery</h1>
            </div>
            <nav class="mt-6">
                <a href="dashboard.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-tachometer-alt mr-4"></i> Dashboard
                </a>
                <a href="bookings.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-book-open mr-4"></i> Bookings
                </a>
                <a href="inventory.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-boxes mr-4"></i> Inventory
                </a>
                <a href="expenses.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-gradient-to-r from-blue-500 to-violet-500 text-white shadow-md">
                    <i class="fas fa-dollar-sign mr-4"></i> Expenses
                </a>
                <a href="reports.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-chart-line mr-4"></i> Reports
                </a>
                <a href="products.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-cookie-bite mr-4"></i> Products
                </a>
            </nav>
            <div class="p-6 mt-auto">
                <a href="../auth/logout.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-red-500 text-white shadow-md hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-4"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Topbar -->
            <header class="flex justify-between items-center p-4 bg-white dark:bg-gray-800 shadow-md">
                <div class="flex items-center">
                    <button id="menu-btn" class="text-gray-600 dark:text-gray-300 focus:outline-none md:hidden">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                        <i class="fas fa-sun"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                            <i class="fas fa-bell"></i>
                        </button>
                        <span class="absolute right-0 top-0 h-2.5 w-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></span>
                    </div>
                    <div class="relative">
                        <button class="flex items-center focus:outline-none">
                            <img src="https://via.placeholder.com/40" alt="avatar" class="h-10 w-10 rounded-full shadow-md">
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-8">
                <h2 class="text-4xl font-bold text-gray-800 dark:text-white mb-6">Manage Expenses</h2>
                
                <!-- Log Expense Form -->
                <div class="bg-white/30 dark:bg-gray-800/30 backdrop-blur-lg rounded-2xl shadow-2xl p-8 mb-8">
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">Log New Expense</h3>
                    <form method="POST">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="relative">
                                <i class="fas fa-tag absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="text" id="expense_type" name="expense_type" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Expense Type" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-dollar-sign absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="number" step="0.01" id="amount" name="amount" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Amount" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-calendar-alt absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="date" id="date" name="date" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" required>
                            </div>
                        </div>
                        <div class="mt-6 relative">
                            <i class="fas fa-align-left absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                            <textarea id="description" name="description" rows="3" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Description"></textarea>
                        </div>
                        <div class="mt-6 text-right">
                            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">
                                Log Expense
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Expenses List -->
                <div class="space-y-4">
                    <?php foreach ($expenses as $expense): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 flex items-center justify-between transform hover:scale-102 transition-transform duration-300">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-receipt text-xl text-blue-500"></i>
                            </div>
                            <div>
                                <p class="font-bold text-lg text-gray-800 dark:text-white"><?php echo htmlspecialchars($expense['expense_type']); ?></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo htmlspecialchars($expense['description']); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg text-red-500">-$<?php echo number_format($expense['amount'], 2); ?></p>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo date('M d, Y', strtotime($expense['date'])); ?></p>
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                            <a href="edit_expense.php?id=<?php echo $expense['id']; ?>" class="px-3 py-2 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition-colors duration-300"><i class="fas fa-edit"></i></a>
                            <a href="delete_expense.php?id=<?php echo $expense['id']; ?>" class="px-3 py-2 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition-colors duration-300" onclick="return confirm('Are you sure you want to delete this expense?');"><i class="fas fa-trash"></i></a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </main>
        </div>
    </div>
    <script>
        // Dark mode toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark');
            const isDarkMode = document.body.classList.contains('dark');
            themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>';
            localStorage.setItem('darkMode', isDarkMode);
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }

        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.querySelector('aside');
        menuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('hidden');
        });
    </script>
</body>
</html>
