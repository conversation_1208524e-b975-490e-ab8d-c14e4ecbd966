<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Edit Product</title>
    <link href="../src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

$id = sanitize_input($_GET['id']);
$stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
$stmt->execute([$id]);
$product = $stmt->fetch();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name']);
    $category = sanitize_input($_POST['category']);
    $unit_price = sanitize_input($_POST['unit_price']);
    $unit = sanitize_input($_POST['unit']);
    $stock_quantity = sanitize_input($_POST['stock_quantity']);
    $reorder_level = sanitize_input($_POST['reorder_level']);

    $stmt = $pdo->prepare("UPDATE products SET name = ?, category = ?, unit_price = ?, unit = ?, stock_quantity = ?, reorder_level = ? WHERE id = ?");
    $stmt->execute([$name, $category, $unit_price, $unit, $stock_quantity, $reorder_level, $id]);

    header("Location: inventory.php");
    exit();
}
?>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-200 font-sans">
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg hidden md:block">
            <div class="p-6 flex items-center justify-center">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">Bakery</h1>
            </div>
            <nav class="mt-6">
                <a href="dashboard.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-tachometer-alt mr-4"></i> Dashboard
                </a>
                <a href="bookings.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-book-open mr-4"></i> Bookings
                </a>
                <a href="inventory.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-gradient-to-r from-blue-500 to-violet-500 text-white shadow-md">
                    <i class="fas fa-boxes mr-4"></i> Inventory
                </a>
                <a href="expenses.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-dollar-sign mr-4"></i> Expenses
                </a>
                <a href="reports.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-chart-line mr-4"></i> Reports
                </a>
                <a href="products.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-cookie-bite mr-4"></i> Products
                </a>
            </nav>
            <div class="p-6 mt-auto">
                <a href="../auth/logout.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-red-500 text-white shadow-md hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-4"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Topbar -->
            <header class="flex justify-between items-center p-4 bg-white dark:bg-gray-800 shadow-md">
                <div class="flex items-center">
                    <button id="menu-btn" class="text-gray-600 dark:text-gray-300 focus:outline-none md:hidden">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                        <i class="fas fa-sun"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                            <i class="fas fa-bell"></i>
                        </button>
                        <span class="absolute right-0 top-0 h-2.5 w-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></span>
                    </div>
                    <div class="relative">
                        <button class="flex items-center focus:outline-none">
                            <img src="https://i.pravatar.cc/40" alt="avatar" class="h-10 w-10 rounded-full shadow-md">
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-8">
                <h2 class="text-4xl font-bold text-gray-800 dark:text-white mb-6">Edit Product</h2>
                <div class="bg-white/30 dark:bg-gray-800/30 backdrop-blur-lg rounded-2xl shadow-2xl p-8">
                    <form method="POST">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="relative">
                                <i class="fas fa-tag absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Product Name" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-sitemap absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <select id="category" name="category" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none appearance-none" required>
                                    <option value="finished" <?php if ($product['category'] === 'finished') echo 'selected'; ?> class="bg-gray-800">Finished Product</option>
                                    <option value="ingredient" <?php if ($product['category'] === 'ingredient') echo 'selected'; ?> class="bg-gray-800">Ingredient</option>
                                </select>
                            </div>
                            <div class="relative">
                                <i class="fas fa-dollar-sign absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="number" step="0.01" id="unit_price" name="unit_price" value="<?php echo $product['unit_price']; ?>" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Unit Price" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-balance-scale absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="text" id="unit" name="unit" value="<?php echo htmlspecialchars($product['unit']); ?>" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Unit" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-boxes absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="number" id="stock_quantity" name="stock_quantity" value="<?php echo $product['stock_quantity']; ?>" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Stock Quantity" required>
                            </div>
                            <div class="relative">
                                <i class="fas fa-sort-amount-down absolute text-gray-400 dark:text-gray-500 top-3 left-4"></i>
                                <input type="number" id="reorder_level" name="reorder_level" value="<?php echo $product['reorder_level']; ?>" class="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Reorder Level" required>
                            </div>
                        </div>
                        <div class="mt-6 text-right">
                            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">
                                Update Product
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
