<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Your Treats - Our Bakery</title>
    <link href="src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<?php
require_once 'config.php';
$stmt = $pdo->query("SELECT * FROM products WHERE category = 'finished' AND stock_quantity > 0 ORDER BY name");
$products = $stmt->fetchAll();
?>
<body class="bg-gradient-to-br from-blue-500 to-violet-500 font-sans">
    <div class="container mx-auto p-6 min-h-screen flex flex-col items-center justify-center">
        <header class="text-center mb-8">
            <a href="index.php" class="text-5xl font-extrabold tracking-tight text-white sm:text-6xl lg:text-7xl">
                Book Your <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-violet-400">Treats</span>
            </a>
            <p class="text-lg text-gray-200 mt-2">Secure your favorite baked goods in advance.</p>
        </header>

        <main class="w-full max-w-2xl">
            <div class="bg-white/30 dark:bg-gray-800/30 backdrop-blur-lg rounded-2xl shadow-2xl p-8">
                <h2 class="text-3xl font-bold text-white text-center mb-8">Place Your Order</h2>
                <form action="booking_handler.php" method="POST" class="space-y-6">
                    <div class="relative">
                        <i class="fas fa-user absolute text-white/70 top-3 left-4"></i>
                        <input type="text" id="customer_name" name="customer_name" class="w-full pl-12 pr-4 py-3 bg-white/20 text-white rounded-lg border border-white/30 focus:ring-2 focus:ring-blue-400 focus:outline-none placeholder-white/70" placeholder="Full Name" required>
                    </div>
                    <div class="relative">
                        <i class="fas fa-phone absolute text-white/70 top-3 left-4"></i>
                        <input type="tel" id="phone" name="phone" class="w-full pl-12 pr-4 py-3 bg-white/20 text-white rounded-lg border border-white/30 focus:ring-2 focus:ring-blue-400 focus:outline-none placeholder-white/70" placeholder="Phone Number" required>
                    </div>
                    <div class="relative">
                        <i class="fas fa-cookie-bite absolute text-white/70 top-3 left-4"></i>
                        <select id="product" name="product_id" class="w-full pl-12 pr-4 py-3 bg-white/20 text-white rounded-lg border border-white/30 focus:ring-2 focus:ring-blue-400 focus:outline-none appearance-none" required>
                            <option value="" class="bg-gray-800">-- Select a Product --</option>
                            <?php foreach ($products as $product): ?>
                            <option value="<?php echo $product['id']; ?>" class="bg-gray-800"><?php echo htmlspecialchars($product['name']); ?> - $<?php echo number_format($product['unit_price'], 2); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="relative">
                            <i class="fas fa-box absolute text-white/70 top-3 left-4"></i>
                            <input type="number" id="quantity" name="quantity" min="1" class="w-full pl-12 pr-4 py-3 bg-white/20 text-white rounded-lg border border-white/30 focus:ring-2 focus:ring-blue-400 focus:outline-none placeholder-white/70" placeholder="Quantity" required>
                        </div>
                        <div class="relative">
                            <i class="fas fa-calendar-alt absolute text-white/70 top-3 left-4"></i>
                            <input type="datetime-local" id="booking_date" name="booking_date" class="w-full pl-12 pr-4 py-3 bg-white/20 text-white rounded-lg border border-white/30 focus:ring-2 focus:ring-blue-400 focus:outline-none" required>
                        </div>
                    </div>
                    <div class="text-center pt-4">
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-violet-500 hover:from-blue-600 hover:to-violet-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">
                            Book Now
                        </button>
                    </div>
                </form>
            </div>
        </main>

        <footer class="text-center mt-8">
            <p class="text-gray-200">&copy; 2025 Bakery Management System. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>
