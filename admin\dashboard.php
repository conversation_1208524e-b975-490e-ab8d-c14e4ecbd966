<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Bakery Management System</title>
    <link href="../src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

// Fetch dashboard data
$today = date('Y-m-d');

// Total bookings today
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE DATE(booking_date) = ?");
$stmt->execute([$today]);
$total_bookings_today = $stmt->fetchColumn();

// Picked vs Pending
$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE status = 'picked' AND DATE(booking_date) = ?");
$stmt->execute([$today]);
$picked_bookings = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE status = 'pending' AND DATE(booking_date) = ?");
$stmt->execute([$today]);
$pending_bookings = $stmt->fetchColumn();

// Low stock items
$stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE stock_quantity < reorder_level");
$stmt->execute();
$low_stock_items = $stmt->fetchColumn();

// Today's Profit
$stmt = $pdo->prepare("SELECT SUM(total_amount) FROM sales WHERE DATE(sale_date) = ?");
$stmt->execute([$today]);
$todays_profit = $stmt->fetchColumn();
$todays_profit = $todays_profit ? $todays_profit : 0;

?>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-200 font-sans">
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg hidden md:block">
            <div class="p-6 flex items-center justify-center">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">Bakery</h1>
            </div>
            <nav class="mt-6">
                <a href="dashboard.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-gradient-to-r from-blue-500 to-violet-500 text-white shadow-md">
                    <i class="fas fa-tachometer-alt mr-4"></i> Dashboard
                </a>
                <a href="bookings.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-book-open mr-4"></i> Bookings
                </a>
                <a href="inventory.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-boxes mr-4"></i> Inventory
                </a>
                <a href="expenses.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-dollar-sign mr-4"></i> Expenses
                </a>
                <a href="reports.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-chart-line mr-4"></i> Reports
                </a>
                <a href="products.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-cookie-bite mr-4"></i> Products
                </a>
            </nav>
            <div class="p-6 mt-auto">
                <a href="../auth/logout.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-red-500 text-white shadow-md hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-4"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Topbar -->
            <header class="flex justify-between items-center p-4 bg-white dark:bg-gray-800 shadow-md">
                <div class="flex items-center">
                    <button id="menu-btn" class="text-gray-600 dark:text-gray-300 focus:outline-none md:hidden">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                        <i class="fas fa-sun"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                            <i class="fas fa-bell"></i>
                        </button>
                        <span class="absolute right-0 top-0 h-2.5 w-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></span>
                    </div>
                    <div class="relative">
                        <button class="flex items-center focus:outline-none">
                            <img src="https://i.pravatar.cc/40" alt="avatar" class="h-10 w-10 rounded-full shadow-md">
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-8">
                <h2 class="text-4xl font-bold text-gray-800 dark:text-white mb-6">Dashboard</h2>
                <!-- Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Card 1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-500 dark:text-gray-400">Total Bookings Today</h3>
                                <p class="text-4xl font-bold text-gray-800 dark:text-white mt-2"><?php echo $total_bookings_today; ?></p>
                            </div>
                            <div class="text-4xl text-violet-500"><i class="fas fa-calendar-check"></i></div>
                        </div>
                    </div>
                    <!-- Card 2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-500 dark:text-gray-400">Picked vs Pending</h3>
                                <p class="text-4xl font-bold text-gray-800 dark:text-white mt-2"><?php echo $picked_bookings; ?> / <?php echo $pending_bookings; ?></p>
                            </div>
                            <div class="text-4xl text-blue-500"><i class="fas fa-tasks"></i></div>
                        </div>
                    </div>
                    <!-- Card 3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-500 dark:text-gray-400">Low Stock Items</h3>
                                <p class="text-4xl font-bold text-red-500 mt-2"><?php echo $low_stock_items; ?></p>
                            </div>
                            <div class="text-4xl text-red-500"><i class="fas fa-exclamation-triangle"></i></div>
                        </div>
                    </div>
                    <!-- Card 4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-500 dark:text-gray-400">Today's Profit</h3>
                                <p class="text-4xl font-bold text-green-500 mt-2">$<?php echo number_format($todays_profit, 2); ?></p>
                            </div>
                            <div class="text-4xl text-green-500"><i class="fas fa-hand-holding-usd"></i></div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="mt-10 grid grid-cols-1 lg:grid-cols-5 gap-8">
                    <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-4">Expense vs Income</h3>
                        <canvas id="profitChart"></canvas>
                    </div>
                    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-4">Booking Status</h3>
                        <canvas id="bookingStatusChart"></canvas>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Dark mode toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark');
            const isDarkMode = document.body.classList.contains('dark');
            themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>';
            localStorage.setItem('darkMode', isDarkMode);
            updateChartColors(isDarkMode);
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }

        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.querySelector('aside');
        menuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('hidden');
        });

        const isDarkMode = () => document.body.classList.contains('dark');


        // Chart.js implementation
        const profitCtx = document.getElementById('profitChart').getContext('2d');
        const profitChart = new Chart(profitCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Income',
                    data: [120, 150, 180, 220, 200, 250, 280],
                    borderColor: '#8B5CF6',
                    backgroundColor: 'rgba(139, 92, 246, 0.2)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Expenses',
                    data: [80, 90, 70, 100, 110, 95, 120],
                    borderColor: '#EC4899',
                    backgroundColor: 'rgba(236, 72, 153, 0.2)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 0
                },
                scales: {
                    x: { 
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { color: '#4B5563' }
                    },
                    y: {
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { color: '#4B5563' }
                    }
                }
            }
        });

        const bookingStatusCtx = document.getElementById('bookingStatusChart').getContext('2d');
        const bookingStatusChart = new Chart(bookingStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Picked', 'Pending', 'Expired'],
                datasets: [{
                    data: [8, 4, 2],
                    backgroundColor: ['#8B5CF6', '#F59E0B', '#EF4444'],
                    borderColor: 'transparent'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 0
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#4B5563'
                        }
                    }
                }
            }
        });

    </script>
</body>
</html>
