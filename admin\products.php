<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Products</title>
    <link href="../src/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<?php
require_once '../config.php';
require_once '../includes/functions.php';
check_auth();

$stmt = $pdo->query("SELECT * FROM products WHERE category = 'finished' ORDER BY name");
$products = $stmt->fetchAll();
?>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-200 font-sans">
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg hidden md:block">
            <div class="p-6 flex items-center justify-center">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">Bakery</h1>
            </div>
            <nav class="mt-6">
                <a href="dashboard.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-tachometer-alt mr-4"></i> Dashboard
                </a>
                <a href="bookings.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-book-open mr-4"></i> Bookings
                </a>
                <a href="inventory.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-boxes mr-4"></i> Inventory
                </a>
                <a href="expenses.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-dollar-sign mr-4"></i> Expenses
                </a>
                <a href="reports.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 transform hover:translate-x-2">
                    <i class="fas fa-chart-line mr-4"></i> Reports
                </a>
                <a href="products.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-gradient-to-r from-blue-500 to-violet-500 text-white shadow-md">
                    <i class="fas fa-cookie-bite mr-4"></i> Products
                </a>
            </nav>
            <div class="p-6 mt-auto">
                <a href="../auth/logout.php" class="block py-3 px-6 text-lg rounded-r-full transition duration-300 bg-red-500 text-white shadow-md hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-4"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Topbar -->
            <header class="flex justify-between items-center p-4 bg-white dark:bg-gray-800 shadow-md">
                <div class="flex items-center">
                    <button id="menu-btn" class="text-gray-600 dark:text-gray-300 focus:outline-none md:hidden">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button id="theme-toggle" class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                        <i class="fas fa-sun"></i>
                    </button>
                    <div class="relative">
                        <button class="text-gray-600 dark:text-gray-300 mr-5 text-xl">
                            <i class="fas fa-bell"></i>
                        </button>
                        <span class="absolute right-0 top-0 h-2.5 w-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></span>
                    </div>
                    <div class="relative">
                        <button class="flex items-center focus:outline-none">
                            <img src="https://via.placeholder.com/40" alt="avatar" class="h-10 w-10 rounded-full shadow-md">
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-4xl font-bold text-gray-800 dark:text-white">Manage Products</h2>
                    <button class="px-6 py-3 bg-gradient-to-r from-blue-500 to-violet-500 text-white rounded-lg shadow-md hover:from-blue-600 hover:to-violet-600 transition-all duration-300 flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New Product
                    </button>
                </div>

                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php foreach ($products as $product): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-transform duration-300 flex flex-col justify-between">
                        <div>
                            <div class="flex justify-between items-start mb-4">
                                <h3 class="text-xl font-bold text-gray-800 dark:text-white"><?php echo htmlspecialchars($product['name']); ?></h3>
                                <span class="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-violet-500">$<?php echo number_format($product['unit_price'], 2); ?></span>
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Finished Product</p>
                            
                            <?php if ($product['stock_quantity'] > 0): ?>
                                <div class="flex items-center text-green-500">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span class="font-semibold">Available</span>
                                </div>
                            <?php else: ?>
                                <div class="flex items-center text-red-500">
                                    <i class="fas fa-times-circle mr-2"></i>
                                    <span class="font-semibold">Not Available</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="edit_product.php?id=<?php echo $product['id']; ?>" class="px-4 py-2 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition-colors duration-300"><i class="fas fa-edit"></i></a>
                            <a href="delete_product.php?id=<?php echo $product['id']; ?>" class="px-4 py-2 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition-colors duration-300" onclick="return confirm('Are you sure you want to delete this product?');"><i class="fas fa-trash"></i></a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </main>
        </div>
    </div>
    <script>
        // Dark mode toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark');
            const isDarkMode = document.body.classList.contains('dark');
            themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-moon"></i>' : '<i class="fas fa-sun"></i>';
            localStorage.setItem('darkMode', isDarkMode);
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }

        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.querySelector('aside');
        menuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('hidden');
        });
    </script>
</body>
</html>
